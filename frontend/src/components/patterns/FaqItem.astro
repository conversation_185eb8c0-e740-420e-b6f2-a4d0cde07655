---
interface Props {
  question: string
}

const { question } = Astro.props
---

<details>
  <summary>
    {question}
  </summary>
  <p>
    <slot />
  </p>
</details>

<style>
  details {
    display: block;
    border-radius: var(--border-radius);
    font-size: 1.2em;
  }

  summary {
    text-decoration: none;
    font-size: 1.2em;
    color: var(--color__indigo--500);
    transition: all 0.25s;
  }

  summary:hover {
    color: var(--color__indigo--700);
    cursor: pointer;
  }

  details:hover {
    cursor: pointer;
  }
</style>
